#!/usr/bin/env python3
"""
Data extraction engine for structured data extraction system.
Handles AI-powered extraction using both custom and LangChain approaches.
"""

import os
import json
import re
from typing import List, Dict
from openai import OpenAI
from dotenv import load_dotenv

# LangChain imports for structured data extraction
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field
from typing import List as TypingList

# Load environment variables
load_dotenv()


# Pydantic models for structured data extraction
class SparePart(BaseModel):
    """Model for spare parts data extraction."""
    equipment_name: str = Field(description="Name of the equipment that the spare part belongs to")
    part_name: str = Field(description="Name of the spare part for procurement")
    part_number: str = Field(description="Part number associated with the spare part")
    drawing_number: str = Field(default="", description="Drawing number from assembly drawings")
    position_number: str = Field(default="", description="Position number from assembly drawings")
    quantity: str = Field(default="", description="Working quantity of the spare part")
    units: str = Field(default="", description="Units for the working quantity")
    materials: str = Field(default="", description="Materials associated with the spare part")
    remarks: str = Field(default="", description="Remarks associated with the spare part")
    spare_part_title: str = Field(default="", description="Title of the spare parts table")
    pdf_reference: int = Field(description="PDF page number where the spare part details are found")


class SparePartsList(BaseModel):
    """List of spare parts."""
    parts: TypingList[SparePart] = Field(description="List of extracted spare parts")


class DataExtractor:
    """Main class for extracting structured data from PDF chunks."""
    
    def __init__(self):
        """Initialize the extractor with API clients."""
        # Initialize OpenAI client
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("Missing OpenAI API key in environment variables")
        
        self.openai_client = OpenAI(api_key=openai_api_key)
        self.llm_model = "gpt-4o-mini"
    
    def extract_structured_data(self,
                              chunks: List[Dict],
                              schema: Dict,
                              system_prompt: str,
                              preserve_page_order: bool = True) -> Dict:
        """Extract structured data from chunks using AI (original method)."""
        
        if not chunks:
            return {"error": "No chunks provided", "data": []}
        
        # Sort chunks by page order if requested
        if preserve_page_order:
            chunks.sort(key=lambda x: (x.get("file_name", ""), x.get("page_number", 0)))
        
        # Prepare context from chunks
        context_parts = []
        for chunk in chunks:
            page_info = f"[Page {chunk.get('page_number', 'Unknown')}]"
            text = chunk.get("text_raw", chunk.get("text", ""))
            # Remove base64 images for cleaner processing
            text_clean = re.sub(r'!\[.*?\]\(data:image/[^)]+\)', '[IMAGE]', text)
            context_parts.append(f"{page_info}\n{text_clean}")
        
        context = "\n\n---\n\n".join(context_parts)
        
        # Prepare extraction prompt using the provided system prompt
        extraction_prompt = f"""You are an expert data extraction assistant. Follow the instructions below to extract structured data from the provided PDF content.

CRITICAL REQUIREMENTS:
1. Return ONLY valid JSON - no explanations, no markdown, no additional text
2. The JSON must conform exactly to the provided schema
3. Preserve the order of information as it appears in the document
4. Use null for missing fields rather than omitting them
5. Be precise and accurate - don't hallucinate information

JSON Schema:
{json.dumps(schema, indent=2)}

EXTRACTION INSTRUCTIONS:
{system_prompt}

RESPONSE FORMAT: Return only the JSON data that matches the schema. Start with {{ or [ and end with }} or ]."""

        user_prompt = f"""Extract structured data from this PDF content:

{context}

Remember to follow the schema exactly and maintain the document order."""

        try:
            response = self.openai_client.chat.completions.create(
                model=self.llm_model,
                messages=[
                    {"role": "system", "content": extraction_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=4000
            )
            
            extracted_text = response.choices[0].message.content.strip()

            # Clean up the response - remove markdown code blocks if present
            if extracted_text.startswith('```json'):
                extracted_text = extracted_text[7:]
            if extracted_text.endswith('```'):
                extracted_text = extracted_text[:-3]
            
            extracted_text = extracted_text.strip()

            # Parse JSON response
            try:
                extracted_data = json.loads(extracted_text)
                
                return {
                    "success": True,
                    "data": extracted_data,
                    "total_chunks": len(chunks),
                    "extraction_method": "custom"
                }
                
            except json.JSONDecodeError as e:
                # Try to extract JSON from the response using regex
                json_match = re.search(r'(\[.*\]|\{.*\})', extracted_text, re.DOTALL)
                if json_match:
                    try:
                        extracted_data = json.loads(json_match.group(1))
                        return {
                            "success": True,
                            "data": extracted_data,
                            "total_chunks": len(chunks),
                            "extraction_method": "custom_regex"
                        }
                    except json.JSONDecodeError:
                        pass
                
                return {
                    "error": f"Invalid JSON response: {e}",
                    "raw_response": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text,
                    "data": []
                }
                
        except Exception as e:
            return {
                "error": f"Extraction failed: {e}",
                "data": []
            }
    
    def extract_with_langchain(self, 
                              chunks: List[Dict], 
                              extraction_type: str,
                              system_prompt: str,
                              preserve_page_order: bool = True) -> Dict:
        """Extract structured data using LangChain with Pydantic models."""
        
        if not chunks:
            return {"error": "No chunks provided", "data": []}
        
        # Sort chunks by page order if requested
        if preserve_page_order:
            chunks.sort(key=lambda x: (x.get("file_name", ""), x.get("page_number", 0)))
        
        # Prepare context from chunks
        context_parts = []
        for chunk in chunks:
            page_info = f"Page {chunk.get('page_number', 'Unknown')}"
            content = chunk.get('text_raw', chunk.get('text', chunk.get('content', '')))
            context_parts.append(f"{page_info}:\n{content}")
        
        context = "\n\n---\n\n".join(context_parts)
        
        try:
            # Initialize LangChain components
            llm = ChatOpenAI(
                model="gpt-4o-mini",  # Use the correct model name
                temperature=0.1,
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )
            
            # Select appropriate Pydantic model based on extraction type
            if extraction_type == "spare_parts":
                output_parser = PydanticOutputParser(pydantic_object=SparePartsList)
            else:
                # Fallback to original method for unknown types
                return self.extract_structured_data(chunks, {}, system_prompt, preserve_page_order)
            
            # Create prompt template
            prompt_template = ChatPromptTemplate.from_messages([
                ("system", f"""You are an expert data extraction assistant. Follow the instructions below to extract structured data from the provided PDF content.

CRITICAL REQUIREMENTS:
1. Extract data accurately according to the provided instructions
2. Preserve the order of information as it appears in the document
3. Use empty strings for missing fields rather than omitting them
4. Be precise and accurate - don't hallucinate information

EXTRACTION INSTRUCTIONS:
{system_prompt}

{{format_instructions}}"""),
                ("human", "Extract structured data from the following PDF content:\n\n{context}")
            ])
            
            # Create the chain
            chain = prompt_template | llm | output_parser
            
            # Execute extraction
            result = chain.invoke({
                "context": context,
                "format_instructions": output_parser.get_format_instructions()
            })
            
            # Convert to list format for compatibility
            if hasattr(result, 'parts'):
                data = [part.dict() for part in result.parts]
            else:
                data = result.dict() if hasattr(result, 'dict') else []
            
            return {
                "success": True,
                "data": data,
                "total_chunks": len(chunks),
                "extraction_method": "langchain"
            }
            
        except Exception as e:
            print(f"LangChain extraction error: {e}")
            # Fallback to original method
            return self.extract_structured_data(chunks, {}, system_prompt, preserve_page_order)


    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_') -> Dict:
        """Flatten nested dictionary for CSV export."""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # Convert list to string representation
                items.append((new_key, ', '.join(map(str, v)) if v else ''))
            else:
                items.append((new_key, v))
        return dict(items)


if __name__ == "__main__":
    # Test the data extractor
    print("🧪 Testing Data Extractor")
    print("=" * 40)
    
    try:
        extractor = DataExtractor()
        
        # Create sample chunks for testing
        sample_chunks = [
            {
                "page_number": 1,
                "file_name": "test.pdf",
                "text_raw": "SPARE PARTS LIST\nPump Assembly - Part No: P001\nQuantity: 2 units\nMaterial: Stainless Steel"
            }
        ]
        
        # Test LangChain extraction
        result = extractor.extract_with_langchain(
            chunks=sample_chunks,
            extraction_type="spare_parts",
            system_prompt="Extract spare parts information including part names, numbers, and quantities."
        )
        
        print(f"✅ Extraction successful: {result.get('success', False)}")
        print(f"📊 Items extracted: {len(result.get('data', []))}")
        print(f"🔧 Method: {result.get('extraction_method', 'unknown')}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
