#!/usr/bin/env python3
"""
Comprehensive test script for the organized structured data extraction system.
Tests all functionality including logging, folder organization, and extraction.

Author: AI Assistant
Date: 2024
"""

import sys
import os
import time
import json
from pathlib import Path

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_folder_structure():
    """Test that all required folders exist."""
    print("🧪 Testing Folder Structure...")
    
    required_folders = [
        'src',
        'config',
        'config/system_prompts',
        'config/schemas', 
        'config/search_queries',
        'temp',
        'logs',
        'utils'
    ]
    
    missing_folders = []
    for folder in required_folders:
        if not os.path.exists(folder):
            missing_folders.append(folder)
        else:
            print(f"  ✅ {folder}")
    
    if missing_folders:
        print(f"  ❌ Missing folders: {missing_folders}")
        return False
    
    print("  🎉 All folders exist!")
    return True


def test_module_imports():
    """Test that all modules can be imported correctly."""
    print("\n🧪 Testing Module Imports...")
    
    try:
        from config_loader import load_available_extractions, get_extraction_names
        print("  ✅ config_loader imported")
        
        from search_utils import SearchManager
        print("  ✅ search_utils imported")
        
        from data_extractor import DataExtractor
        print("  ✅ data_extractor imported")
        
        from export_utils import ExportManager
        print("  ✅ export_utils imported")
        
        from ui_components import StructuredQueryUI
        print("  ✅ ui_components imported")
        
        from utils.logger import get_logger
        print("  ✅ logger imported")
        
        print("  🎉 All modules imported successfully!")
        return True
        
    except Exception as e:
        print(f"  ❌ Import error: {e}")
        return False


def test_configuration_loading():
    """Test configuration loading from new folder structure."""
    print("\n🧪 Testing Configuration Loading...")
    
    try:
        from config_loader import load_available_extractions, get_extraction_names
        
        # Test loading extractions
        extractions = load_available_extractions()
        print(f"  ✅ Loaded {len(extractions)} extraction types")
        
        # Test getting names
        names = get_extraction_names()
        print(f"  ✅ Got extraction names: {names}")
        
        # Verify structure
        for key, config in extractions.items():
            required_keys = ['name', 'system_prompt', 'schema', 'search_queries']
            missing_keys = [k for k in required_keys if k not in config]
            if missing_keys:
                print(f"  ❌ Missing keys in {key}: {missing_keys}")
                return False
            else:
                print(f"  ✅ {key} configuration complete")
        
        print("  🎉 Configuration loading successful!")
        return True
        
    except Exception as e:
        print(f"  ❌ Configuration error: {e}")
        return False


def test_logging_system():
    """Test the logging system."""
    print("\n🧪 Testing Logging System...")
    
    try:
        from utils.logger import get_logger, log_info, log_error
        
        # Test logger creation
        logger = get_logger("test_logger")
        print("  ✅ Logger created")
        
        # Test different log levels
        logger.info("Test info message")
        logger.debug("Test debug message")
        logger.warning("Test warning message")
        
        # Test convenience functions
        log_info("Test convenience info")
        
        # Test structured logging
        logger.log_processing_step("Test Step", "STARTED", test_param="value")
        logger.log_processing_step("Test Step", "COMPLETED")
        
        # Check if log file was created
        log_files = list(Path("logs").glob("extraction_*.log"))
        if log_files:
            print(f"  ✅ Log file created: {log_files[0]}")
        else:
            print("  ❌ No log file found")
            return False
        
        print("  🎉 Logging system working!")
        return True
        
    except Exception as e:
        print(f"  ❌ Logging error: {e}")
        return False


def test_search_manager():
    """Test SearchManager initialization and basic functionality."""
    print("\n🧪 Testing SearchManager...")
    
    try:
        from search_utils import SearchManager
        
        # Test initialization
        search_manager = SearchManager()
        print("  ✅ SearchManager initialized")
        
        # Test getting available files
        files = search_manager.get_available_files()
        print(f"  ✅ Found {len(files)} PDF files")
        
        if files:
            print(f"  📄 Available files: {files[:3]}...")  # Show first 3
        
        print("  🎉 SearchManager working!")
        return True
        
    except Exception as e:
        print(f"  ❌ SearchManager error: {e}")
        return False


def test_data_extractor():
    """Test DataExtractor initialization."""
    print("\n🧪 Testing DataExtractor...")
    
    try:
        from data_extractor import DataExtractor
        
        # Test initialization
        extractor = DataExtractor()
        print("  ✅ DataExtractor initialized")
        
        print("  🎉 DataExtractor working!")
        return True
        
    except Exception as e:
        print(f"  ❌ DataExtractor error: {e}")
        return False


def test_export_manager():
    """Test ExportManager initialization."""
    print("\n🧪 Testing ExportManager...")
    
    try:
        from export_utils import ExportManager
        
        # Test initialization
        export_manager = ExportManager()
        print("  ✅ ExportManager initialized")
        
        # Test CSV preview with sample data
        sample_data = [
            {"name": "Test Item 1", "value": 100},
            {"name": "Test Item 2", "value": 200}
        ]
        
        csv_preview = export_manager.create_csv_preview(sample_data)
        if csv_preview and "Test Item 1" in csv_preview:
            print("  ✅ CSV preview generation working")
        else:
            print("  ❌ CSV preview generation failed")
            return False
        
        print("  🎉 ExportManager working!")
        return True
        
    except Exception as e:
        print(f"  ❌ ExportManager error: {e}")
        return False


def test_ui_components():
    """Test UI components initialization."""
    print("\n🧪 Testing UI Components...")
    
    try:
        from ui_components import StructuredQueryUI
        
        # Test initialization
        ui = StructuredQueryUI()
        print("  ✅ StructuredQueryUI initialized")
        
        # Test getting available files
        files = ui.get_available_files()
        print(f"  ✅ UI can access {len(files)} files")
        
        print("  🎉 UI Components working!")
        return True
        
    except Exception as e:
        print(f"  ❌ UI Components error: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Comprehensive System Test")
    print("=" * 50)
    
    tests = [
        test_folder_structure,
        test_module_imports,
        test_configuration_loading,
        test_logging_system,
        test_search_manager,
        test_data_extractor,
        test_export_manager,
        test_ui_components
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! System is working correctly.")
        print(f"🌐 Access the interface at: http://localhost:7862")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
