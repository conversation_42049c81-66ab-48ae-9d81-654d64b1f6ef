#!/usr/bin/env python3
"""
Comprehensive logging utility for the structured data extraction system.
Provides detailed logging for debugging and monitoring the extraction process.

Author: AI Assistant
Date: 2024
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import json


class ExtractionLogger:
    """Enhanced logger for the extraction system with structured logging."""
    
    def __init__(self, name: str = "extraction_system", log_level: str = "INFO"):
        """Initialize the logger with file and console handlers."""
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Create logs directory if it doesn't exist
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)
        
        # Clear existing handlers to avoid duplicates
        self.logger.handlers.clear()
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        console_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # File handler for detailed logs
        log_file = self.logs_dir / f"extraction_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        
        # Console handler for important messages
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(console_formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        self.logger.info(f"🚀 Logger initialized - Log file: {log_file}")
    
    def debug(self, message: str, **kwargs):
        """Log debug message with optional context."""
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message with optional context."""
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with optional context."""
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with optional context."""
        self._log_with_context(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with optional context."""
        self._log_with_context(logging.CRITICAL, message, **kwargs)
    
    def _log_with_context(self, level: int, message: str, **kwargs):
        """Log message with additional context."""
        if kwargs:
            context = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
            message = f"{message} | {context}"
        self.logger.log(level, message)
    
    def log_extraction_start(self, extraction_type: str, file_name: str, **params):
        """Log the start of an extraction process."""
        self.info(
            f"🔍 EXTRACTION STARTED",
            extraction_type=extraction_type,
            file_name=file_name,
            **params
        )
    
    def log_extraction_end(self, extraction_type: str, success: bool, items_extracted: int = 0, **params):
        """Log the end of an extraction process."""
        status = "SUCCESS" if success else "FAILED"
        emoji = "✅" if success else "❌"
        self.info(
            f"{emoji} EXTRACTION {status}",
            extraction_type=extraction_type,
            items_extracted=items_extracted,
            **params
        )
    
    def log_search_operation(self, query: str, chunks_found: int, search_type: str = "semantic"):
        """Log search operation details."""
        self.info(
            f"🔍 SEARCH COMPLETED",
            query=query,
            chunks_found=chunks_found,
            search_type=search_type
        )
    
    def log_ai_request(self, model: str, tokens_used: Optional[int] = None, **params):
        """Log AI model request details."""
        self.info(
            f"🤖 AI REQUEST",
            model=model,
            tokens_used=tokens_used,
            **params
        )
    
    def log_data_export(self, format_type: str, items_count: int, file_path: Optional[str] = None):
        """Log data export operation."""
        self.info(
            f"📤 DATA EXPORTED",
            format=format_type,
            items_count=items_count,
            file_path=file_path
        )
    
    def log_error_with_traceback(self, message: str, error: Exception, **kwargs):
        """Log error with full traceback."""
        import traceback
        tb = traceback.format_exc()
        self.error(f"{message} | Error: {str(error)}")
        self.debug(f"Traceback: {tb}", **kwargs)
    
    def log_processing_step(self, step: str, status: str = "STARTED", **params):
        """Log individual processing steps."""
        emoji = {
            "STARTED": "🔄",
            "COMPLETED": "✅", 
            "FAILED": "❌",
            "SKIPPED": "⏭️"
        }.get(status, "ℹ️")
        
        self.info(f"{emoji} {step} - {status}", **params)
    
    def log_configuration(self, config: Dict[str, Any]):
        """Log system configuration."""
        self.info("⚙️ CONFIGURATION LOADED")
        for key, value in config.items():
            if isinstance(value, (dict, list)):
                self.debug(f"Config {key}: {json.dumps(value, indent=2)}")
            else:
                self.debug(f"Config {key}: {value}")
    
    def log_chunk_processing(self, chunk_index: int, total_chunks: int, page_number: int, content_length: int):
        """Log chunk processing details."""
        self.debug(
            f"📄 PROCESSING CHUNK {chunk_index}/{total_chunks}",
            page_number=page_number,
            content_length=content_length
        )


# Global logger instance
logger = ExtractionLogger()


def get_logger(name: str = "extraction_system") -> ExtractionLogger:
    """Get a logger instance."""
    return ExtractionLogger(name)


# Convenience functions
def log_info(message: str, **kwargs):
    """Quick info logging."""
    logger.info(message, **kwargs)


def log_error(message: str, error: Optional[Exception] = None, **kwargs):
    """Quick error logging."""
    if error:
        logger.log_error_with_traceback(message, error, **kwargs)
    else:
        logger.error(message, **kwargs)


def log_debug(message: str, **kwargs):
    """Quick debug logging."""
    logger.debug(message, **kwargs)
