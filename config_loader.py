#!/usr/bin/env python3
"""
Configuration loader for structured data extraction system.
Handles loading of system prompts, schemas, and search queries from folders.
"""

import os
import json
import glob
from pathlib import Path
from typing import Dict, List


def load_available_extractions() -> Dict:
    """Load available extraction types from system_prompts, schemas, and search_queries folders."""
    extractions = {}
    
    # Get all system prompt files
    prompt_files = glob.glob("system_prompts/*.txt")
    
    for prompt_file in prompt_files:
        extraction_name = Path(prompt_file).stem
        schema_file = f"schemas/{extraction_name}.json"
        search_queries_file = f"search_queries/{extraction_name}.txt"
        
        if os.path.exists(schema_file):
            # Load system prompt
            with open(prompt_file, 'r', encoding='utf-8') as f:
                system_prompt = f.read().strip()
            
            # Load schema
            with open(schema_file, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            
            # Load search queries if available
            search_queries = []
            if os.path.exists(search_queries_file):
                with open(search_queries_file, 'r', encoding='utf-8') as f:
                    search_queries = [line.strip() for line in f.readlines() if line.strip()]
            
            extractions[extraction_name] = {
                'name': extraction_name.replace('_', ' ').title(),
                'system_prompt': system_prompt,
                'schema': schema,
                'search_queries': search_queries
            }
    
    return extractions


def get_extraction_names() -> List[str]:
    """Get list of available extraction type names."""
    extractions = load_available_extractions()
    return [config['name'] for config in extractions.values()]


def get_extraction_config(extraction_type: str) -> Dict:
    """Get configuration for a specific extraction type."""
    extractions = load_available_extractions()
    return extractions.get(extraction_type, {})


def validate_extraction_folders() -> Dict[str, bool]:
    """Validate that required folders exist."""
    folders = {
        'system_prompts': os.path.exists('system_prompts'),
        'schemas': os.path.exists('schemas'),
        'search_queries': os.path.exists('search_queries')
    }
    return folders


def create_extraction_folders():
    """Create required folders if they don't exist."""
    folders = ['system_prompts', 'schemas', 'search_queries', 'temp']
    for folder in folders:
        os.makedirs(folder, exist_ok=True)
    print(f"✅ Created/verified folders: {', '.join(folders)}")


if __name__ == "__main__":
    # Test the configuration loader
    print("🧪 Testing Configuration Loader")
    print("=" * 40)
    
    # Validate folders
    folder_status = validate_extraction_folders()
    print(f"📁 Folder status: {folder_status}")
    
    # Load extractions
    extractions = load_available_extractions()
    print(f"📋 Available extractions: {list(extractions.keys())}")
    
    for name, config in extractions.items():
        print(f"  - {config['name']}: {len(config.get('search_queries', []))} search queries")
