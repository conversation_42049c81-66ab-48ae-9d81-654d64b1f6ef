#!/usr/bin/env python3
"""
Search utilities for structured data extraction system.
Handles semantic search, query generation, and chunk retrieval.
"""

import os
from typing import List, Dict
from openai import OpenAI
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class SearchManager:
    """Manages search operations and chunk retrieval."""
    
    def __init__(self):
        """Initialize search manager with API clients."""
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")
        
        if not supabase_url or not supabase_key:
            raise ValueError("Missing Supabase credentials in environment variables")
        
        self.supabase: Client = create_client(supabase_url, supabase_key)
        
        # Initialize OpenAI client
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("Missing OpenAI API key in environment variables")
        
        self.openai_client = OpenAI(api_key=openai_api_key)
        
        # Configuration - use same model as stored embeddings
        self.embedding_model = "text-embedding-3-small"
        self.embedding_dimensions = 1536
    
    def get_available_files(self) -> List[str]:
        """Get list of available PDF files in the database."""
        try:
            result = self.supabase.table("pdf_documents").select("file_name").execute()
            files = list(set([row["file_name"] for row in result.data if row["file_name"]]))
            return sorted(files)
        except Exception as e:
            print(f"❌ Error fetching files: {e}")
            return []
    
    def get_file_pages_ordered(self, file_name: str) -> List[Dict]:
        """Get all pages for a file in ascending page order."""
        try:
            result = self.supabase.table("pdf_documents").select(
                "id, file_name, page_number, text, text_raw, metadata"
            ).eq("file_name", file_name).order("page_number", desc=False).execute()
            
            print(f"📄 Retrieved {len(result.data)} pages for {file_name}")
            return result.data
        except Exception as e:
            print(f"❌ Error fetching pages: {e}")
            return []
    
    def semantic_search(self, query: str, file_name: str = None, limit: int = 10) -> List[Dict]:
        """Perform semantic search to find relevant chunks."""
        try:
            print(f"🔍 Performing semantic search for: '{query}'")
            
            # Generate embedding for the query
            embedding_response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=query,
                dimensions=self.embedding_dimensions
            )
            query_embedding = embedding_response.data[0].embedding
            
            # Perform similarity search using Supabase function
            rpc_params = {
                "query_embedding": query_embedding,
                "match_threshold": 0.1,
                "match_count": limit
            }

            # Add file filter if specified
            if file_name:
                rpc_params["input_file_name"] = file_name

            result = self.supabase.rpc("match_documents", rpc_params).execute()
            
            # Process results
            processed_results = []
            for row in result.data:
                processed_results.append({
                    "id": row.get("id"),
                    "file_name": row.get("file_name"),
                    "page_number": row.get("page_number"),
                    "text": row.get("text"),
                    "text_raw": row.get("text_raw"),
                    "similarity": row.get("similarity", 0),
                    "metadata": row.get("metadata", {})
                })
            
            # Sort by page order if file is specified
            if file_name:
                processed_results.sort(key=lambda x: (x.get("file_name", ""), x.get("page_number", 0)))

            print(f"🔍 Found {len(processed_results)} relevant chunks")
            return processed_results

        except Exception as e:
            print(f"❌ Semantic search error: {e}")
            return []
    
    def generate_smart_search_query(self, extraction_type: str, search_queries: List[str]) -> str:
        """Generate an intelligent search query using LLM based on predefined queries."""
        if not search_queries:
            return ""
        
        # Use a subset of search queries to generate a focused query
        sample_queries = search_queries[:10]  # Use first 10 queries
        
        try:
            prompt = f"""Based on the following predefined search terms for {extraction_type} extraction, generate a single, focused search query that would best find relevant content in a PDF document:

Predefined terms: {', '.join(sample_queries)}

Generate a concise search query (2-4 words) that combines the most important concepts. Return only the search query, no explanation."""

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a search query optimization expert."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=50
            )
            
            generated_query = response.choices[0].message.content.strip()
            return generated_query
            
        except Exception as e:
            print(f"Error generating search query: {e}")
            # Fallback to first few predefined queries
            return " ".join(search_queries[:3])
    
    def save_chunks_to_temp(self, chunks: List[Dict], filename: str) -> str:
        """Save retrieved chunks to temp folder in markdown format."""
        import os
        from datetime import datetime
        
        # Create temp directory if it doesn't exist
        os.makedirs("temp", exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_filename = f"temp/chunks_{filename}_{timestamp}.md"
        
        # Format chunks as markdown
        markdown_content = f"# Retrieved Chunks for {filename}\n\n"
        markdown_content += f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        markdown_content += f"**Total Chunks:** {len(chunks)}\n\n"
        markdown_content += "---\n\n"
        
        for i, chunk in enumerate(chunks, 1):
            page_num = chunk.get('page_number', 'Unknown')
            file_name = chunk.get('file_name', 'Unknown')
            similarity = chunk.get('similarity', 0)
            content = chunk.get('text_raw', chunk.get('text', '')) or ''
            
            markdown_content += f"## Chunk {i}\n\n"
            markdown_content += f"**File:** {file_name}  \n"
            markdown_content += f"**Page:** {page_num}  \n"
            if similarity > 0:
                markdown_content += f"**Similarity:** {similarity:.3f}  \n"
            markdown_content += f"**Content Length:** {len(content)} characters\n\n"
            markdown_content += "### Content\n\n"
            markdown_content += f"```\n{content}\n```\n\n"
            markdown_content += "---\n\n"
        
        # Save to file
        with open(temp_filename, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"💾 Saved chunks to: {temp_filename}")
        return temp_filename


if __name__ == "__main__":
    # Test the search manager
    print("🧪 Testing Search Manager")
    print("=" * 40)
    
    try:
        search_manager = SearchManager()
        
        # Test file listing
        files = search_manager.get_available_files()
        print(f"📁 Available files: {files}")
        
        if files:
            # Test search query generation
            sample_queries = ["spare parts", "components", "parts list"]
            smart_query = search_manager.generate_smart_search_query("spare_parts", sample_queries)
            print(f"🧠 Generated query: '{smart_query}'")
            
            # Test semantic search
            chunks = search_manager.semantic_search(smart_query, files[0], 3)
            print(f"🔍 Found {len(chunks)} chunks")
            
            if chunks:
                # Test saving to temp
                temp_file = search_manager.save_chunks_to_temp(chunks, "test")
                print(f"💾 Saved to: {temp_file}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
