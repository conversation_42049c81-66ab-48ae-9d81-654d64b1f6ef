#!/usr/bin/env python3
"""
Structured Data Extraction from PDF Chunks

This script provides a comprehensive system for extracting structured data
from PDF chunks stored in Supabase, with efficient page order management
and multiple output formats.

Features:
- Schema-driven extraction using JSON schemas
- Page order preservation (ascending by page number)
- Semantic search for relevant content
- Multiple output formats (JSON, CSV, Excel)
- Gradio interface for user interaction
- Batch processing for efficiency
"""

import json
import os
import csv
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import re

from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client, Client
import gradio as gr

# Load environment variables
load_dotenv()

class StructuredDataExtractor:
    """Main class for extracting structured data from PDF chunks."""
    
    def __init__(self):
        """Initialize the extractor with API clients."""
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.supabase: Client = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_KEY")
        )
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        self.llm_model = os.getenv("LLM_MODEL", "gpt-4o-mini")
        
        # Create output directory
        os.makedirs("extracted_data", exist_ok=True)
        
    def get_available_files(self) -> List[str]:
        """Get list of available PDF files in the database."""
        try:
            result = self.supabase.table("pdf_documents").select("file_name").execute()
            files = list(set([row["file_name"] for row in result.data if row["file_name"]]))
            return sorted(files)
        except Exception as e:
            print(f"❌ Error fetching files: {e}")
            return []
    
    def get_file_pages_ordered(self, file_name: str) -> List[Dict]:
        """Get all pages for a file in ascending page order."""
        try:
            result = self.supabase.table("pdf_documents").select(
                "id, file_name, page_number, text, text_raw, metadata"
            ).eq("file_name", file_name).order("page_number", desc=False).execute()
            
            print(f"📄 Retrieved {len(result.data)} pages for {file_name}")
            return result.data
        except Exception as e:
            print(f"❌ Error fetching pages: {e}")
            return []
    
    def semantic_search(self, query: str, file_name: str = None, limit: int = 10) -> List[Dict]:
        """Perform semantic search to find relevant chunks."""
        try:
            # Generate query embedding
            response = self.openai_client.embeddings.create(
                input=query,
                model=self.embedding_model
            )
            query_embedding = response.data[0].embedding

            # Build search parameters
            search_params = {
                "query_embedding": query_embedding,
                "match_threshold": 0.3,  # Lower threshold for better recall
                "match_count": limit
            }

            if file_name:
                search_params["input_file_name"] = file_name

            # Perform similarity search
            result = self.supabase.rpc("match_documents", search_params).execute()

            # Process results to add text_raw field if missing
            processed_results = []
            for item in result.data:
                # Ensure we have both text and text_raw fields
                if 'text_raw' not in item and 'text' in item:
                    # Remove base64 images from text to create text_raw
                    text_raw = re.sub(r'!\[.*?\]\(data:image/[^)]+\)', '[IMAGE]', item['text'])
                    item['text_raw'] = text_raw
                elif 'text' not in item and 'text_raw' in item:
                    item['text'] = item['text_raw']

                processed_results.append(item)

            # Sort by page number to maintain order
            if processed_results:
                processed_results.sort(key=lambda x: (x.get("file_name", ""), x.get("page_number", 0)))

            print(f"🔍 Found {len(processed_results)} relevant chunks")
            return processed_results

        except Exception as e:
            print(f"❌ Semantic search error: {e}")
            return []
    
    def extract_structured_data(self, 
                              chunks: List[Dict], 
                              schema: Dict, 
                              instruction: str,
                              preserve_page_order: bool = True) -> Dict:
        """Extract structured data from chunks using AI."""
        
        if not chunks:
            return {"error": "No chunks provided", "data": []}
        
        # Sort chunks by page order if requested
        if preserve_page_order:
            chunks.sort(key=lambda x: (x.get("file_name", ""), x.get("page_number", 0)))
        
        # Prepare context from chunks
        context_parts = []
        for chunk in chunks:
            page_info = f"[Page {chunk.get('page_number', 'Unknown')}]"
            text = chunk.get("text_raw", chunk.get("text", ""))
            # Remove base64 images for cleaner processing
            text_clean = re.sub(r'!\[.*?\]\(data:image/[^)]+\)', '[IMAGE]', text)
            context_parts.append(f"{page_info}\n{text_clean}")
        
        context = "\n\n---\n\n".join(context_parts)
        
        # Prepare extraction prompt
        system_prompt = f"""You are an expert data extraction assistant. Extract structured data from the provided PDF content according to the given JSON schema and instructions.

CRITICAL REQUIREMENTS:
1. Return ONLY valid JSON - no explanations, no markdown, no additional text
2. The JSON must conform exactly to the provided schema
3. Preserve the order of information as it appears in the document
4. Use null for missing fields rather than omitting them
5. Be precise and accurate - don't hallucinate information

JSON Schema:
{json.dumps(schema, indent=2)}

Extraction Instructions:
{instruction}

RESPONSE FORMAT: Return only the JSON data that matches the schema. Start with {{ or [ and end with }} or ]."""

        user_prompt = f"""Extract structured data from this PDF content:

{context}

Remember to follow the schema exactly and maintain the document order."""

        try:
            # Use OpenAI models (gpt-4o, gpt-4o-mini, etc.)
            response = self.openai_client.chat.completions.create(
                model=self.llm_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=4000
            )
            
            extracted_text = response.choices[0].message.content.strip()

            # Clean up the response - remove markdown code blocks if present
            if extracted_text.startswith('```json'):
                extracted_text = extracted_text[7:]
            if extracted_text.startswith('```'):
                extracted_text = extracted_text[3:]
            if extracted_text.endswith('```'):
                extracted_text = extracted_text[:-3]

            extracted_text = extracted_text.strip()

            # Try to parse as JSON
            try:
                extracted_data = json.loads(extracted_text)
                return {
                    "success": True,
                    "data": extracted_data,
                    "pages_processed": len(chunks),
                    "source_pages": [f"Page {c.get('page_number')}" for c in chunks]
                }
            except json.JSONDecodeError as e:
                # Try to find JSON within the response
                json_match = re.search(r'(\[.*\]|\{.*\})', extracted_text, re.DOTALL)
                if json_match:
                    try:
                        extracted_data = json.loads(json_match.group(1))
                        return {
                            "success": True,
                            "data": extracted_data,
                            "pages_processed": len(chunks),
                            "source_pages": [f"Page {c.get('page_number')}" for c in chunks]
                        }
                    except json.JSONDecodeError:
                        pass

                return {
                    "error": f"Invalid JSON response: {e}",
                    "raw_response": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text,
                    "data": []
                }
                
        except Exception as e:
            return {
                "error": f"Extraction failed: {e}",
                "data": []
            }
    
    def export_to_csv(self, data: List[Dict], filename: str) -> str:
        """Export structured data to CSV format."""
        if not data:
            return None
            
        output_path = f"extracted_data/{filename}.csv"
        
        try:
            # Flatten nested dictionaries for CSV
            flattened_data = []
            for item in data:
                flat_item = self._flatten_dict(item)
                flattened_data.append(flat_item)
            
            # Write to CSV
            if flattened_data:
                fieldnames = flattened_data[0].keys()
                with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(flattened_data)
                
                print(f"✅ CSV exported to {output_path}")
                return output_path
        except Exception as e:
            print(f"❌ CSV export error: {e}")
            return None
    
    def export_to_excel(self, data: List[Dict], filename: str) -> str:
        """Export structured data to Excel format."""
        if not data:
            return None
            
        output_path = f"extracted_data/{filename}.xlsx"
        
        try:
            # Convert to DataFrame
            df = pd.json_normalize(data)
            
            # Write to Excel
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Extracted_Data', index=False)
                
            print(f"✅ Excel exported to {output_path}")
            return output_path
        except Exception as e:
            print(f"❌ Excel export error: {e}")
            return None
    
    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_') -> Dict:
        """Flatten nested dictionary for CSV export."""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # Convert lists to comma-separated strings
                items.append((new_key, ', '.join(map(str, v)) if v else ''))
            else:
                items.append((new_key, v))
        return dict(items)

# Global extractor instance
extractor = StructuredDataExtractor()

def validate_json_schema(schema_text: str) -> tuple[bool, str, Dict]:
    """Validate JSON schema format."""
    try:
        schema = json.loads(schema_text)
        return True, "✅ Valid JSON schema", schema
    except json.JSONDecodeError as e:
        return False, f"❌ Invalid JSON: {e}", {}

def process_extraction_request(schema_text: str, 
                             instruction: str, 
                             file_name: str,
                             search_query: str = "",
                             use_semantic_search: bool = True,
                             max_chunks: int = 20) -> tuple[str, str, str]:
    """Process the extraction request and return results."""
    
    # Validate schema
    is_valid, validation_msg, schema = validate_json_schema(schema_text)
    if not is_valid:
        return validation_msg, "", ""
    
    # Get data chunks
    if use_semantic_search and search_query.strip():
        chunks = extractor.semantic_search(search_query, file_name, max_chunks)
        method_used = f"Semantic search for: '{search_query}'"
    else:
        chunks = extractor.get_file_pages_ordered(file_name)
        if max_chunks and len(chunks) > max_chunks:
            chunks = chunks[:max_chunks]
        method_used = f"Sequential processing (first {len(chunks)} pages)"
    
    if not chunks:
        return "❌ No relevant content found", "", ""
    
    # Extract structured data
    result = extractor.extract_structured_data(chunks, schema, instruction)
    
    if result.get("success"):
        # Format results
        status = f"""✅ Extraction completed successfully!
        
📊 **Processing Summary:**
- Method: {method_used}
- Pages processed: {result['pages_processed']}
- Source pages: {', '.join(result['source_pages'])}

🎯 **Results:** {len(result['data']) if isinstance(result['data'], list) else 1} items extracted"""
        
        # Format JSON output
        json_output = json.dumps(result['data'], indent=2, ensure_ascii=False)
        
        # Generate CSV preview
        csv_preview = ""
        if isinstance(result['data'], list) and result['data']:
            try:
                # Create a simple CSV preview
                sample_data = result['data'][:5]  # First 5 items
                flattened = [extractor._flatten_dict(item) for item in sample_data]
                if flattened:
                    headers = list(flattened[0].keys())
                    csv_lines = [','.join(headers)]
                    for item in flattened:
                        row = [str(item.get(h, '')) for h in headers]
                        csv_lines.append(','.join(f'"{cell}"' for cell in row))
                    csv_preview = '\n'.join(csv_lines)
            except Exception as e:
                csv_preview = f"CSV preview error: {e}"
        
        return status, json_output, csv_preview
    else:
        error_msg = f"❌ Extraction failed: {result.get('error', 'Unknown error')}"
        return error_msg, result.get('raw_response', ''), ""

# Example schemas for common use cases
EXAMPLE_SCHEMAS = {
    "Parts List": {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "part_number": {"type": "string"},
                "description": {"type": "string"},
                "quantity": {"type": ["string", "number"]},
                "material": {"type": "string"},
                "page_number": {"type": "number"}
            }
        }
    },
    "Equipment Specifications": {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "equipment_name": {"type": "string"},
                "model": {"type": "string"},
                "specifications": {"type": "string"},
                "dimensions": {"type": "string"},
                "weight": {"type": "string"},
                "features": {"type": "array", "items": {"type": "string"}}
            }
        }
    },
    "Table Data": {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "row_data": {"type": "object"},
                "table_title": {"type": "string"},
                "page_number": {"type": "number"}
            }
        }
    }
}

def create_gradio_interface():
    """Create and configure the Gradio interface."""

    # Get available files
    available_files = extractor.get_available_files()
    if not available_files:
        available_files = ["No files found"]

    # Create the interface
    with gr.Blocks(title="📊 Structured Data Extraction", theme=gr.themes.Soft()) as interface:
        gr.Markdown("""
        # 🔍 PDF Structured Data Extraction

        Extract structured data from your PDF documents using AI-powered analysis.
        Define your extraction schema and get results in JSON, CSV, or Excel format.
        """)

        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 📋 Extraction Configuration")

                # File selection
                file_dropdown = gr.Dropdown(
                    choices=available_files,
                    label="📁 Select PDF File",
                    value=available_files[0] if available_files else None
                )

                # Schema input with examples
                schema_examples = gr.Dropdown(
                    choices=list(EXAMPLE_SCHEMAS.keys()),
                    label="📝 Example Schemas (optional)",
                    value=None
                )

                schema_input = gr.Code(
                    label="🔧 JSON Schema",
                    language="json",
                    value=json.dumps(EXAMPLE_SCHEMAS["Parts List"], indent=2),
                    lines=15
                )

                # Instruction input
                instruction_input = gr.Textbox(
                    label="📖 Extraction Instructions",
                    placeholder="Describe what you want to extract...",
                    value="Extract all parts with their numbers, descriptions, quantities, and materials from the document.",
                    lines=3
                )

                # Search options
                with gr.Accordion("🔍 Search Options", open=False):
                    use_search = gr.Checkbox(
                        label="Use Semantic Search",
                        value=True,
                        info="Find relevant content using AI search"
                    )
                    search_query = gr.Textbox(
                        label="Search Query",
                        placeholder="e.g., 'parts list', 'specifications', 'table data'",
                        value="parts list components"
                    )
                    max_chunks = gr.Slider(
                        minimum=5,
                        maximum=50,
                        value=20,
                        step=5,
                        label="Max Pages to Process"
                    )

                # Extract button
                extract_btn = gr.Button("🚀 Extract Data", variant="primary", size="lg")

            with gr.Column(scale=2):
                gr.Markdown("### 📊 Extraction Results")

                # Status output
                status_output = gr.Markdown(label="Status")

                # Results tabs
                with gr.Tabs():
                    with gr.Tab("📄 JSON Output"):
                        json_output = gr.Code(
                            label="Structured Data (JSON)",
                            language="json",
                            lines=20
                        )

                        # Download buttons
                        with gr.Row():
                            json_download = gr.File(label="📥 Download JSON", visible=False)
                            csv_download = gr.File(label="📥 Download CSV", visible=False)
                            excel_download = gr.File(label="📥 Download Excel", visible=False)

                    with gr.Tab("📊 CSV Preview"):
                        csv_output = gr.Textbox(
                            label="CSV Preview",
                            lines=15,
                            max_lines=20,
                            show_copy_button=True
                        )

                    with gr.Tab("📈 Data Summary"):
                        summary_output = gr.Markdown()

        # Event handlers
        def load_example_schema(example_name):
            if example_name and example_name in EXAMPLE_SCHEMAS:
                return json.dumps(EXAMPLE_SCHEMAS[example_name], indent=2)
            return gr.update()

        def handle_extraction(schema_text, instruction, file_name, search_query, use_search, max_chunks):
            """Handle the extraction process and return results."""
            try:
                # Process extraction
                status, json_result, csv_preview = process_extraction_request(
                    schema_text, instruction, file_name, search_query, use_search, max_chunks
                )

                # Generate download files if successful
                json_file = None
                csv_file = None
                excel_file = None
                summary = "No data extracted"

                if json_result and "❌" not in status:
                    try:
                        data = json.loads(json_result)
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        base_filename = f"{file_name.replace('.pdf', '')}_{timestamp}"

                        # Save JSON
                        json_path = f"extracted_data/{base_filename}.json"
                        with open(json_path, 'w', encoding='utf-8') as f:
                            json.dump(data, f, indent=2, ensure_ascii=False)
                        json_file = json_path

                        # Save CSV and Excel if data is a list
                        if isinstance(data, list) and data:
                            csv_path = extractor.export_to_csv(data, base_filename)
                            excel_path = extractor.export_to_excel(data, base_filename)
                            csv_file = csv_path
                            excel_file = excel_path

                            # Generate summary
                            summary = f"""
### 📈 Extraction Summary

- **Total Records**: {len(data)}
- **File Processed**: {file_name}
- **Schema Fields**: {len(data[0].keys()) if data else 0}
- **Export Formats**: JSON, CSV, Excel available
                            """
                        else:
                            summary = f"""
### 📈 Extraction Summary

- **Data Type**: {type(data).__name__}
- **File Processed**: {file_name}
- **Export Formats**: JSON available
                            """
                    except Exception as e:
                        summary = f"Error generating downloads: {e}"

                return (
                    status,
                    json_result,
                    csv_preview,
                    summary,
                    gr.File(value=json_file, visible=bool(json_file)),
                    gr.File(value=csv_file, visible=bool(csv_file)),
                    gr.File(value=excel_file, visible=bool(excel_file))
                )

            except Exception as e:
                error_msg = f"❌ Processing error: {str(e)}"
                return error_msg, "", "", error_msg, gr.File(visible=False), gr.File(visible=False), gr.File(visible=False)

        # Wire up events
        schema_examples.change(
            fn=load_example_schema,
            inputs=[schema_examples],
            outputs=[schema_input]
        )

        extract_btn.click(
            fn=handle_extraction,
            inputs=[schema_input, instruction_input, file_dropdown, search_query, use_search, max_chunks],
            outputs=[status_output, json_output, csv_output, summary_output, json_download, csv_download, excel_download]
        )

    return interface

if __name__ == "__main__":
    print("🚀 Starting Structured Data Extraction Interface...")

    # Create and launch interface
    interface = create_gradio_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
