#!/usr/bin/env python3
"""
Structured Data Extraction System for PDF Documents - Main Entry Point

This is the main entry point for the modular structured data extraction system.
The system has been broken down into focused modules for better maintainability:

- config_loader.py: Configuration and folder management
- search_utils.py: Search and chunk retrieval operations  
- data_extractor.py: AI-powered data extraction engine
- export_utils.py: Data export functionality
- ui_components.py: Gradio interface components

This file provides backward compatibility and serves as the main launcher.

Author: AI Assistant
Date: 2024
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.ui_components import create_and_launch_interface

# For backward compatibility, import key functions
from src.config_loader import load_available_extractions, get_extraction_names
from src.search_utils import SearchManager
from src.data_extractor import DataExtractor
from src.export_utils import ExportManager


def process_extraction_request(extraction_type: str,
                             extraction_config: dict,
                             file_name: str,
                             use_semantic_search: bool = True,
                             max_chunks: int = 20):
    """
    Backward compatibility function for processing extraction requests.
    This function maintains the same interface as the original monolithic version.
    """
    from src.ui_components import StructuredQueryUI
    
    # Create UI instance to access processing methods
    ui = StructuredQueryUI()
    
    # Process the request
    status, json_result, csv_preview, chunk_display = ui.process_extraction_request(
        extraction_type=extraction_type,
        file_name=file_name,
        use_semantic_search=use_semantic_search,
        max_chunks=max_chunks
    )
    
    return status, json_result, csv_preview


def main():
    """Main function to launch the interface."""
    print("🚀 Launching Modular Structured Data Extraction System")
    print("=" * 60)
    print("📁 Modules loaded:")
    print("  - config_loader.py: Configuration management")
    print("  - search_utils.py: Search and retrieval")
    print("  - data_extractor.py: AI extraction engine")
    print("  - export_utils.py: Data export utilities")
    print("  - ui_components.py: Gradio interface")
    print("=" * 60)
    
    create_and_launch_interface()


if __name__ == "__main__":
    main()
