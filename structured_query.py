#!/usr/bin/env python3
"""
Structured Data Extraction from PDF Chunks

This script provides a comprehensive system for extracting structured data
from PDF chunks stored in Supabase, with efficient page order management
and multiple output formats.

Features:
- Schema-driven extraction using JSON schemas
- Page order preservation (ascending by page number)
- Semantic search for relevant content
- Multiple output formats (JSON, CSV, Excel)
- Gradio interface for user interaction
- Batch processing for efficiency
"""

import json
import os
import csv
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import re
import glob

from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client, Client
import gradio as gr

# LangChain imports for structured data extraction
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field
from typing import List as TypingList

# Load environment variables
load_dotenv()


def load_available_extractions():
    """Load available extraction types from system_prompts, schemas, and search_queries folders."""
    extractions = {}

    # Get all system prompt files
    prompt_files = glob.glob("system_prompts/*.txt")

    for prompt_file in prompt_files:
        extraction_name = Path(prompt_file).stem
        schema_file = f"schemas/{extraction_name}.json"
        search_queries_file = f"search_queries/{extraction_name}.txt"

        if os.path.exists(schema_file):
            # Load system prompt
            with open(prompt_file, 'r', encoding='utf-8') as f:
                system_prompt = f.read().strip()

            # Load schema
            with open(schema_file, 'r', encoding='utf-8') as f:
                schema = json.load(f)

            # Load search queries if available
            search_queries = []
            if os.path.exists(search_queries_file):
                with open(search_queries_file, 'r', encoding='utf-8') as f:
                    search_queries = [line.strip() for line in f.readlines() if line.strip()]

            extractions[extraction_name] = {
                'name': extraction_name.replace('_', ' ').title(),
                'system_prompt': system_prompt,
                'schema': schema,
                'search_queries': search_queries
            }

    return extractions


def generate_smart_search_query(extraction_type: str, search_queries: List[str], openai_client) -> str:
    """Generate an intelligent search query using LLM based on predefined queries."""
    if not search_queries:
        return ""

    # Use a subset of search queries to generate a focused query
    sample_queries = search_queries[:10]  # Use first 10 queries

    try:
        prompt = f"""Based on the following predefined search terms for {extraction_type} extraction, generate a single, focused search query that would best find relevant content in a PDF document:

Predefined terms: {', '.join(sample_queries)}

Generate a concise search query (2-4 words) that combines the most important concepts. Return only the search query, no explanation."""

        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a search query optimization expert."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=50
        )

        generated_query = response.choices[0].message.content.strip()
        return generated_query

    except Exception as e:
        print(f"Error generating search query: {e}")
        # Fallback to first few predefined queries
        return " ".join(search_queries[:3])


# Pydantic models for structured data extraction
class SparePart(BaseModel):
    """Model for spare parts data extraction."""
    equipment_name: str = Field(description="Name of the equipment that the spare part belongs to")
    part_name: str = Field(description="Name of the spare part for procurement")
    part_number: str = Field(description="Part number associated with the spare part")
    drawing_number: str = Field(default="", description="Drawing number from assembly drawings")
    position_number: str = Field(default="", description="Position number from assembly drawings")
    quantity: str = Field(default="", description="Working quantity of the spare part")
    units: str = Field(default="", description="Units for the working quantity")
    materials: str = Field(default="", description="Materials associated with the spare part")
    remarks: str = Field(default="", description="Remarks associated with the spare part")
    spare_part_title: str = Field(default="", description="Title of the spare parts table")
    pdf_reference: int = Field(description="PDF page number where the spare part details are found")

class SparePartsList(BaseModel):
    """List of spare parts."""
    parts: TypingList[SparePart] = Field(description="List of extracted spare parts")


class StructuredDataExtractor:
    """Main class for extracting structured data from PDF chunks."""

    def __init__(self):
        """Initialize the extractor with API clients."""
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.supabase: Client = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_KEY")
        )
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        self.llm_model = os.getenv("LLM_MODEL", "gpt-4o-mini")
        
        # Create output directory
        os.makedirs("extracted_data", exist_ok=True)
        
    def get_available_files(self) -> List[str]:
        """Get list of available PDF files in the database."""
        try:
            result = self.supabase.table("pdf_documents").select("file_name").execute()
            files = list(set([row["file_name"] for row in result.data if row["file_name"]]))
            return sorted(files)
        except Exception as e:
            print(f"❌ Error fetching files: {e}")
            return []
    
    def get_file_pages_ordered(self, file_name: str) -> List[Dict]:
        """Get all pages for a file in ascending page order."""
        try:
            result = self.supabase.table("pdf_documents").select(
                "id, file_name, page_number, text, text_raw, metadata"
            ).eq("file_name", file_name).order("page_number", desc=False).execute()
            
            print(f"📄 Retrieved {len(result.data)} pages for {file_name}")
            return result.data
        except Exception as e:
            print(f"❌ Error fetching pages: {e}")
            return []
    
    def semantic_search(self, query: str, file_name: str = None, limit: int = 10) -> List[Dict]:
        """Perform semantic search to find relevant chunks."""
        try:
            # Generate query embedding
            response = self.openai_client.embeddings.create(
                input=query,
                model=self.embedding_model
            )
            query_embedding = response.data[0].embedding

            # Build search parameters
            search_params = {
                "query_embedding": query_embedding,
                "match_threshold": 0.3,  # Lower threshold for better recall
                "match_count": limit
            }

            if file_name:
                search_params["input_file_name"] = file_name

            # Perform similarity search
            result = self.supabase.rpc("match_documents", search_params).execute()

            # Process results to add text_raw field if missing
            processed_results = []
            for item in result.data:
                # Ensure we have both text and text_raw fields
                if 'text_raw' not in item and 'text' in item:
                    # Remove base64 images from text to create text_raw
                    text_raw = re.sub(r'!\[.*?\]\(data:image/[^)]+\)', '[IMAGE]', item['text'])
                    item['text_raw'] = text_raw
                elif 'text' not in item and 'text_raw' in item:
                    item['text'] = item['text_raw']

                processed_results.append(item)

            # Sort by page number to maintain order
            if processed_results:
                processed_results.sort(key=lambda x: (x.get("file_name", ""), x.get("page_number", 0)))

            print(f"🔍 Found {len(processed_results)} relevant chunks")
            return processed_results

        except Exception as e:
            print(f"❌ Semantic search error: {e}")
            return []
    
    def extract_structured_data(self,
                              chunks: List[Dict],
                              schema: Dict,
                              system_prompt: str,
                              preserve_page_order: bool = True) -> Dict:
        """Extract structured data from chunks using AI."""
        
        if not chunks:
            return {"error": "No chunks provided", "data": []}
        
        # Sort chunks by page order if requested
        if preserve_page_order:
            chunks.sort(key=lambda x: (x.get("file_name", ""), x.get("page_number", 0)))
        
        # Prepare context from chunks
        context_parts = []
        for chunk in chunks:
            page_info = f"[Page {chunk.get('page_number', 'Unknown')}]"
            text = chunk.get("text_raw", chunk.get("text", ""))
            # Remove base64 images for cleaner processing
            text_clean = re.sub(r'!\[.*?\]\(data:image/[^)]+\)', '[IMAGE]', text)
            context_parts.append(f"{page_info}\n{text_clean}")
        
        context = "\n\n---\n\n".join(context_parts)
        
        # Prepare extraction prompt using the provided system prompt
        extraction_prompt = f"""You are an expert data extraction assistant. Follow the instructions below to extract structured data from the provided PDF content.

CRITICAL REQUIREMENTS:
1. Return ONLY valid JSON - no explanations, no markdown, no additional text
2. The JSON must conform exactly to the provided schema
3. Preserve the order of information as it appears in the document
4. Use null for missing fields rather than omitting them
5. Be precise and accurate - don't hallucinate information

JSON Schema:
{json.dumps(schema, indent=2)}

EXTRACTION INSTRUCTIONS:
{system_prompt}

RESPONSE FORMAT: Return only the JSON data that matches the schema. Start with {{ or [ and end with }} or ]."""

        user_prompt = f"""Extract structured data from this PDF content:

{context}

Remember to follow the schema exactly and maintain the document order."""

        try:
            # Use OpenAI models (gpt-4o, gpt-4o-mini, etc.)
            response = self.openai_client.chat.completions.create(
                model=self.llm_model,
                messages=[
                    {"role": "system", "content": extraction_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=4000
            )
            
            extracted_text = response.choices[0].message.content.strip()

            # Clean up the response - remove markdown code blocks if present
            if extracted_text.startswith('```json'):
                extracted_text = extracted_text[7:]
            if extracted_text.startswith('```'):
                extracted_text = extracted_text[3:]
            if extracted_text.endswith('```'):
                extracted_text = extracted_text[:-3]

            extracted_text = extracted_text.strip()

            # Try to parse as JSON
            try:
                extracted_data = json.loads(extracted_text)
                return {
                    "success": True,
                    "data": extracted_data,
                    "pages_processed": len(chunks),
                    "source_pages": [f"Page {c.get('page_number')}" for c in chunks]
                }
            except json.JSONDecodeError as e:
                # Try to find JSON within the response
                json_match = re.search(r'(\[.*\]|\{.*\})', extracted_text, re.DOTALL)
                if json_match:
                    try:
                        extracted_data = json.loads(json_match.group(1))
                        return {
                            "success": True,
                            "data": extracted_data,
                            "pages_processed": len(chunks),
                            "source_pages": [f"Page {c.get('page_number')}" for c in chunks]
                        }
                    except json.JSONDecodeError:
                        pass

                return {
                    "error": f"Invalid JSON response: {e}",
                    "raw_response": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text,
                    "data": []
                }
                
        except Exception as e:
            return {
                "error": f"Extraction failed: {e}",
                "data": []
            }

    def extract_with_langchain(self,
                              chunks: List[Dict],
                              extraction_type: str,
                              system_prompt: str,
                              preserve_page_order: bool = True) -> Dict:
        """Extract structured data using LangChain with Pydantic models."""

        if not chunks:
            return {"error": "No chunks provided", "data": []}

        # Sort chunks by page order if requested
        if preserve_page_order:
            chunks.sort(key=lambda x: (x.get("file_name", ""), x.get("page_number", 0)))

        # Prepare context from chunks
        context_parts = []
        for chunk in chunks:
            page_info = f"Page {chunk.get('page_number', 'Unknown')}"
            content = chunk.get('text', chunk.get('content', ''))
            context_parts.append(f"{page_info}:\n{content}")

        context = "\n\n---\n\n".join(context_parts)

        try:
            # Initialize LangChain components
            llm = ChatOpenAI(
                model="gpt-4o-mini",  # Use the correct model name
                temperature=0.1,
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )

            # Select appropriate Pydantic model based on extraction type
            if extraction_type == "spare_parts":
                output_parser = PydanticOutputParser(pydantic_object=SparePartsList)
            else:
                # Fallback to original method for unknown types
                return self.extract_structured_data(chunks, {}, system_prompt, preserve_page_order)

            # Create prompt template
            prompt_template = ChatPromptTemplate.from_messages([
                ("system", f"""You are an expert data extraction assistant. Follow the instructions below to extract structured data from the provided PDF content.

CRITICAL REQUIREMENTS:
1. Extract data accurately according to the provided instructions
2. Preserve the order of information as it appears in the document
3. Use empty strings for missing fields rather than omitting them
4. Be precise and accurate - don't hallucinate information

EXTRACTION INSTRUCTIONS:
{system_prompt}

{{format_instructions}}"""),
                ("human", "Extract structured data from the following PDF content:\n\n{context}")
            ])

            # Create the chain
            chain = prompt_template | llm | output_parser

            # Execute extraction
            result = chain.invoke({
                "context": context,
                "format_instructions": output_parser.get_format_instructions()
            })

            # Convert to list format for compatibility
            if hasattr(result, 'parts'):
                data = [part.dict() for part in result.parts]
            else:
                data = result.dict() if hasattr(result, 'dict') else []

            return {
                "success": True,
                "data": data,
                "total_chunks": len(chunks),
                "extraction_method": "langchain"
            }

        except Exception as e:
            print(f"LangChain extraction error: {e}")
            # Fallback to original method
            return self.extract_structured_data(chunks, {}, system_prompt, preserve_page_order)

    def export_to_csv(self, data: List[Dict], filename: str) -> str:
        """Export structured data to CSV format."""
        if not data:
            return None
            
        output_path = f"extracted_data/{filename}.csv"
        
        try:
            # Flatten nested dictionaries for CSV
            flattened_data = []
            for item in data:
                flat_item = self._flatten_dict(item)
                flattened_data.append(flat_item)
            
            # Write to CSV
            if flattened_data:
                fieldnames = flattened_data[0].keys()
                with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(flattened_data)
                
                print(f"✅ CSV exported to {output_path}")
                return output_path
        except Exception as e:
            print(f"❌ CSV export error: {e}")
            return None
    
    def export_to_excel(self, data: List[Dict], filename: str) -> str:
        """Export structured data to Excel format."""
        if not data:
            return None
            
        output_path = f"extracted_data/{filename}.xlsx"
        
        try:
            # Convert to DataFrame
            df = pd.json_normalize(data)
            
            # Write to Excel
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Extracted_Data', index=False)
                
            print(f"✅ Excel exported to {output_path}")
            return output_path
        except Exception as e:
            print(f"❌ Excel export error: {e}")
            return None
    
    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_') -> Dict:
        """Flatten nested dictionary for CSV export."""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # Convert lists to comma-separated strings
                items.append((new_key, ', '.join(map(str, v)) if v else ''))
            else:
                items.append((new_key, v))
        return dict(items)

# Global extractor instance
extractor = StructuredDataExtractor()

def validate_json_schema(schema_text: str) -> tuple[bool, str, Dict]:
    """Validate JSON schema format."""
    try:
        schema = json.loads(schema_text)
        return True, "✅ Valid JSON schema", schema
    except json.JSONDecodeError as e:
        return False, f"❌ Invalid JSON: {e}", {}

def process_extraction_request(extraction_type: str,
                             extraction_config: dict,
                             file_name: str,
                             use_semantic_search: bool = True,
                             max_chunks: int = 20) -> tuple[str, str, str]:
    """Process the extraction request and return results."""

    # Extract configuration
    schema = extraction_config['schema']
    system_prompt = extraction_config['system_prompt']
    search_queries = extraction_config.get('search_queries', [])

    # Generate smart search query if semantic search is enabled
    search_query = ""
    if use_semantic_search and search_queries:
        search_query = generate_smart_search_query(extraction_type, search_queries, extractor.openai_client)
        print(f"🧠 Generated search query: '{search_query}'")

    # Get data chunks
    if use_semantic_search and search_query.strip():
        chunks = extractor.semantic_search(search_query, file_name, max_chunks)
        method_used = f"Smart semantic search for: '{search_query}'"
    else:
        chunks = extractor.get_file_pages_ordered(file_name)
        if max_chunks and len(chunks) > max_chunks:
            chunks = chunks[:max_chunks]
        method_used = f"Sequential processing (first {len(chunks)} pages)"

    if not chunks:
        return "❌ No relevant content found", "", ""

    # Extract structured data using LangChain
    result = extractor.extract_with_langchain(chunks, extraction_type, system_prompt)
    
    if result.get("success"):
        # Format results
        status = f"""✅ Extraction completed successfully!
        
📊 **Processing Summary:**
- Method: {method_used}
- Pages processed: {result['pages_processed']}
- Source pages: {', '.join(result['source_pages'])}

🎯 **Results:** {len(result['data']) if isinstance(result['data'], list) else 1} items extracted"""
        
        # Format JSON output
        json_output = json.dumps(result['data'], indent=2, ensure_ascii=False)
        
        # Generate CSV preview
        csv_preview = ""
        if isinstance(result['data'], list) and result['data']:
            try:
                # Create a simple CSV preview
                sample_data = result['data'][:5]  # First 5 items
                flattened = [extractor._flatten_dict(item) for item in sample_data]
                if flattened:
                    headers = list(flattened[0].keys())
                    csv_lines = [','.join(headers)]
                    for item in flattened:
                        row = [str(item.get(h, '')) for h in headers]
                        csv_lines.append(','.join(f'"{cell}"' for cell in row))
                    csv_preview = '\n'.join(csv_lines)
            except Exception as e:
                csv_preview = f"CSV preview error: {e}"
        
        return status, json_output, csv_preview
    else:
        error_msg = f"❌ Extraction failed: {result.get('error', 'Unknown error')}"
        return error_msg, result.get('raw_response', ''), ""



def create_gradio_interface():
    """Create and configure the Gradio interface."""

    extractor = StructuredDataExtractor()

    # Get available files
    available_files = extractor.get_available_files()
    if not available_files:
        available_files = ["No files found"]

    # Load available extraction types
    extractions = load_available_extractions()
    extraction_names = list(extractions.keys()) if extractions else ["No extractions available"]

    # Create the interface
    with gr.Blocks(title="📊 Structured Data Extraction", theme=gr.themes.Soft()) as interface:
        gr.Markdown("""
        # 🔍 PDF Structured Data Extraction

        Extract structured data from your PDF documents using AI-powered analysis.
        Define your extraction schema and get results in JSON, CSV, or Excel format.
        """)

        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 📋 Extraction Configuration")

                # File selection
                file_dropdown = gr.Dropdown(
                    choices=available_files,
                    label="📁 Select PDF File",
                    value=available_files[0] if available_files else None
                )

                # Extraction type selection
                extraction_dropdown = gr.Dropdown(
                    choices=extraction_names,
                    label="🎯 Extraction Type",
                    value=extraction_names[0] if extraction_names else None,
                    info="Select the type of data to extract"
                )

                # Processing options
                with gr.Accordion("🔍 Processing Options", open=False):
                    use_search = gr.Checkbox(
                        label="Use Smart Semantic Search",
                        value=True,
                        info="AI will generate optimal search queries based on extraction type"
                    )
                    max_chunks = gr.Slider(
                        minimum=5,
                        maximum=50,
                        value=20,
                        step=5,
                        label="Max Pages to Process"
                    )

                # Extract button
                extract_btn = gr.Button("🚀 Extract Data", variant="primary", size="lg")

            with gr.Column(scale=2):
                gr.Markdown("### 📊 Extraction Results")

                # Status output
                status_output = gr.Markdown(label="Status")

                # Results tabs
                with gr.Tabs():
                    with gr.Tab("📄 JSON Output"):
                        json_output = gr.Code(
                            label="Structured Data (JSON)",
                            language="json",
                            lines=20
                        )

                        # Download buttons
                        with gr.Row():
                            json_download = gr.File(label="📥 Download JSON", visible=False)
                            csv_download = gr.File(label="📥 Download CSV", visible=False)
                            excel_download = gr.File(label="📥 Download Excel", visible=False)

                    with gr.Tab("📊 CSV Preview"):
                        csv_output = gr.Textbox(
                            label="CSV Preview",
                            lines=15,
                            max_lines=20,
                            show_copy_button=True
                        )

                    with gr.Tab("📈 Data Summary"):
                        summary_output = gr.Markdown()

        # Event handlers

        def handle_extraction(extraction_type, file_name, use_search, max_chunks):
            """Handle the extraction process and return results."""
            try:
                # Get extraction configuration
                if extraction_type not in extractions:
                    return "❌ Invalid extraction type selected", "", "", "Error: Invalid extraction type", gr.File(visible=False), gr.File(visible=False), gr.File(visible=False)

                extraction_config = extractions[extraction_type]

                # Process extraction
                status, json_result, csv_preview = process_extraction_request(
                    extraction_type, extraction_config, file_name, use_search, max_chunks
                )

                # Generate download files if successful
                json_file = None
                csv_file = None
                excel_file = None
                summary = "No data extracted"

                if json_result and "❌" not in status:
                    try:
                        data = json.loads(json_result)
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        base_filename = f"{file_name.replace('.pdf', '')}_{timestamp}"

                        # Save JSON
                        json_path = f"extracted_data/{base_filename}.json"
                        with open(json_path, 'w', encoding='utf-8') as f:
                            json.dump(data, f, indent=2, ensure_ascii=False)
                        json_file = json_path

                        # Save CSV and Excel if data is a list
                        if isinstance(data, list) and data:
                            csv_path = extractor.export_to_csv(data, base_filename)
                            excel_path = extractor.export_to_excel(data, base_filename)
                            csv_file = csv_path
                            excel_file = excel_path

                            # Generate summary
                            summary = f"""
### 📈 Extraction Summary

- **Total Records**: {len(data)}
- **File Processed**: {file_name}
- **Schema Fields**: {len(data[0].keys()) if data else 0}
- **Export Formats**: JSON, CSV, Excel available
                            """
                        else:
                            summary = f"""
### 📈 Extraction Summary

- **Data Type**: {type(data).__name__}
- **File Processed**: {file_name}
- **Export Formats**: JSON available
                            """
                    except Exception as e:
                        summary = f"Error generating downloads: {e}"

                return (
                    status,
                    json_result,
                    csv_preview,
                    summary,
                    gr.File(value=json_file, visible=bool(json_file)),
                    gr.File(value=csv_file, visible=bool(csv_file)),
                    gr.File(value=excel_file, visible=bool(excel_file))
                )

            except Exception as e:
                error_msg = f"❌ Processing error: {str(e)}"
                return error_msg, "", "", error_msg, gr.File(visible=False), gr.File(visible=False), gr.File(visible=False)

        # Wire up events
        extract_btn.click(
            fn=handle_extraction,
            inputs=[extraction_dropdown, file_dropdown, use_search, max_chunks],
            outputs=[status_output, json_output, csv_output, summary_output, json_download, csv_download, excel_download]
        )

    return interface

if __name__ == "__main__":
    print("🚀 Starting Structured Data Extraction Interface...")

    # Create and launch interface
    interface = create_gradio_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
