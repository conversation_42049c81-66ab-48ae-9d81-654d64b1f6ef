#!/usr/bin/env python3
"""
Complete functionality test for the modular structured data extraction system.
Tests the full workflow including chunk retrieval, display, and temp storage.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_complete_workflow():
    """Test the complete extraction workflow."""
    print("🧪 Testing Complete Workflow")
    print("=" * 50)
    
    try:
        # Import the UI components
        from ui_components import StructuredQueryUI
        
        # Initialize the UI
        ui = StructuredQueryUI()
        print("✅ UI initialized successfully")
        
        # Get available files
        files = ui.get_available_files()
        print(f"📁 Available files: {files}")
        
        if not files:
            print("⚠️ No files available for testing")
            return True
        
        # Test extraction with chunk display
        test_file = files[0]
        print(f"🔍 Testing with file: {test_file}")
        
        # Process extraction request
        status, json_result, csv_preview, chunk_display = ui.process_extraction_request(
            extraction_type="spare_parts",
            file_name=test_file,
            use_semantic_search=True,
            max_chunks=3  # Small test
        )
        
        print(f"📊 Status length: {len(status)}")
        print(f"📄 JSON result length: {len(json_result)}")
        print(f"📋 CSV preview length: {len(csv_preview)}")
        print(f"📝 Chunk display length: {len(chunk_display)}")
        
        # Check if extraction was successful
        if "✅" in status:
            print("🎉 Extraction successful!")
            
            # Check chunk display
            if "Retrieved Chunks" in chunk_display:
                print("✅ Chunk display generated")
            else:
                print("⚠️ Chunk display may be incomplete")
            
            # Check temp folder
            temp_files = [f for f in os.listdir("temp") if f.endswith(".md")]
            print(f"💾 Temp files created: {len(temp_files)}")
            if temp_files:
                print(f"📄 Latest temp file: {temp_files[-1]}")
            
            return True
        else:
            print("❌ Extraction failed")
            print(f"Status: {status[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Complete workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_chunk_display_features():
    """Test specific chunk display features."""
    print("🧪 Testing Chunk Display Features")
    print("=" * 50)
    
    try:
        from ui_components import StructuredQueryUI
        
        ui = StructuredQueryUI()
        
        # Create sample chunks
        sample_chunks = [
            {
                "page_number": 1,
                "file_name": "test.pdf",
                "text_raw": "This is sample content from page 1 with spare parts information.",
                "similarity": 0.85
            },
            {
                "page_number": 2,
                "file_name": "test.pdf",
                "text_raw": "This is sample content from page 2 with more spare parts data.",
                "similarity": 0.78
            }
        ]
        
        # Test chunk display creation
        chunk_display = ui._create_chunk_display(sample_chunks)
        print(f"✅ Chunk display created: {len(chunk_display)} characters")
        
        # Check for expected content
        expected_elements = [
            "Retrieved Chunks",
            "Chunk 1",
            "Chunk 2",
            "Page:",
            "File:",
            "Similarity:",
            "Content Preview:"
        ]
        
        missing_elements = []
        for element in expected_elements:
            if element not in chunk_display:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"⚠️ Missing elements: {missing_elements}")
        else:
            print("✅ All expected elements present in chunk display")
        
        # Test temp file saving
        temp_file = ui.search_manager.save_chunks_to_temp(sample_chunks, "test")
        print(f"💾 Temp file saved: {temp_file}")
        
        # Verify temp file exists and has content
        if os.path.exists(temp_file):
            with open(temp_file, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"📄 Temp file size: {len(content)} characters")
            
            if "Retrieved Chunks for test" in content:
                print("✅ Temp file has correct header")
            else:
                print("⚠️ Temp file header may be incorrect")
        else:
            print("❌ Temp file not created")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Chunk display test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_modular_integration():
    """Test that all modules work together correctly."""
    print("🧪 Testing Modular Integration")
    print("=" * 50)
    
    try:
        # Test importing all modules
        from config_loader import load_available_extractions
        from search_utils import SearchManager
        from data_extractor import DataExtractor
        from export_utils import ExportManager
        from ui_components import StructuredQueryUI
        
        print("✅ All modules imported successfully")
        
        # Test that they can be initialized together
        config = load_available_extractions()
        search_manager = SearchManager()
        data_extractor = DataExtractor()
        export_manager = ExportManager()
        ui = StructuredQueryUI()
        
        print("✅ All components initialized successfully")
        
        # Test that they share data correctly
        files = search_manager.get_available_files()
        extraction_names = list(config.keys())
        
        print(f"📁 Files from search manager: {len(files)}")
        print(f"📋 Extractions from config: {len(extraction_names)}")
        
        # Test backward compatibility
        from structured_query import process_extraction_request
        print("✅ Backward compatibility function available")
        
        return True
        
    except Exception as e:
        print(f"❌ Modular integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all comprehensive tests."""
    print("🚀 Comprehensive Testing of Modular System")
    print("=" * 60)
    
    tests = [
        test_modular_integration,
        test_chunk_display_features,
        test_complete_workflow
    ]
    
    results = []
    for test in tests:
        results.append(test())
        print()  # Add spacing between tests
    
    print("📊 Final Test Summary")
    print("=" * 60)
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All comprehensive tests passed!")
        print("🚀 The modular system with chunk display is fully functional!")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    # Show file structure
    print("\n📁 Current File Structure:")
    print("=" * 30)
    files = [
        "config_loader.py",
        "search_utils.py", 
        "data_extractor.py",
        "export_utils.py",
        "ui_components.py",
        "structured_query.py"
    ]
    
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size} bytes)")
        else:
            print(f"❌ {file} (missing)")
    
    # Show temp folder
    if os.path.exists("temp"):
        temp_files = os.listdir("temp")
        print(f"\n💾 Temp folder: {len(temp_files)} files")
        for f in temp_files[-3:]:  # Show last 3 files
            print(f"  - {f}")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
