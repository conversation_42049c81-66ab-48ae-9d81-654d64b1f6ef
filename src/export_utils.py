#!/usr/bin/env python3
"""
Export utilities for structured data extraction system.
Handles exporting data to various formats (JSON, CSV, Excel).
"""

import os
import json
import csv
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional


class ExportManager:
    """Manages data export operations."""
    
    def __init__(self):
        """Initialize export manager."""
        # Create exports directory if it doesn't exist
        os.makedirs("exports", exist_ok=True)
    
    def export_to_json(self, data: List[Dict], filename: str) -> str:
        """Export structured data to JSON format."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"exports/{filename}_{timestamp}.json"
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ JSON exported to {output_path}")
            return output_path
        except Exception as e:
            print(f"❌ JSON export error: {e}")
            return None
    
    def export_to_csv(self, data: List[Dict], filename: str) -> str:
        """Export structured data to CSV format."""
        try:
            if not data:
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"exports/{filename}_{timestamp}.csv"
            
            # Flatten nested dictionaries
            flattened_data = [self._flatten_dict(item) for item in data]
            
            # Get all unique keys for CSV headers
            all_keys = set()
            for item in flattened_data:
                all_keys.update(item.keys())
            
            # Write CSV
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                if flattened_data:
                    fieldnames = sorted(all_keys)
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(flattened_data)
                
                print(f"✅ CSV exported to {output_path}")
                return output_path
        except Exception as e:
            print(f"❌ CSV export error: {e}")
            return None
    
    def export_to_excel(self, data: List[Dict], filename: str) -> str:
        """Export structured data to Excel format."""
        try:
            if not data:
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"exports/{filename}_{timestamp}.xlsx"
            
            # Flatten nested dictionaries
            flattened_data = [self._flatten_dict(item) for item in data]
            
            # Create DataFrame
            df = pd.DataFrame(flattened_data)
            
            # Export to Excel
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Extracted_Data', index=False)
                
            print(f"✅ Excel exported to {output_path}")
            return output_path
        except Exception as e:
            print(f"❌ Excel export error: {e}")
            return None
    
    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '_') -> Dict:
        """Flatten nested dictionary for CSV export."""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # Convert list to string representation
                items.append((new_key, ', '.join(map(str, v)) if v else ''))
            else:
                items.append((new_key, v))
        return dict(items)
    
    def generate_export_summary(self, data: List[Dict], file_name: str, extraction_method: str) -> str:
        """Generate a summary of the exported data."""
        if not data:
            return "No data to summarize."
        
        try:
            total_items = len(data)
            
            # Get unique pages if pdf_reference exists
            pages = set()
            for item in data:
                if 'pdf_reference' in item:
                    pages.add(item['pdf_reference'])
            
            # Get sample fields
            sample_fields = list(data[0].keys()) if data else []
            
            summary = f"""
## 📊 Export Summary

**Data Overview:**
- **Total Items Extracted**: {total_items}
- **Source File**: {file_name}
- **Extraction Method**: {extraction_method}
- **Pages Covered**: {len(pages)} pages ({', '.join(map(str, sorted(pages))) if pages else 'N/A'})
- **Fields Extracted**: {len(sample_fields)}

**Available Fields:**
{', '.join(sample_fields[:10])}{'...' if len(sample_fields) > 10 else ''}

**Export Information:**
- **Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Export Formats**: JSON, CSV, Excel available for download
            """
            
            return summary.strip()
            
        except Exception as e:
            return f"Error generating summary: {e}"
    
    def create_csv_preview(self, data: List[Dict], max_rows: int = 5) -> str:
        """Create a CSV preview string for display."""
        if not data:
            return "No data available for preview."
        
        try:
            # Flatten data for CSV format
            flattened = [self._flatten_dict(item) for item in data[:max_rows]]
            
            if not flattened:
                return "No data to preview."
            
            # Get headers
            headers = list(flattened[0].keys())
            
            # Create CSV lines
            csv_lines = [','.join(headers)]
            for item in flattened:
                row = [str(item.get(h, '')) for h in headers]
                csv_lines.append(','.join(f'"{cell}"' for cell in row))
            
            preview = '\n'.join(csv_lines)
            
            if len(data) > max_rows:
                preview += f"\n\n... and {len(data) - max_rows} more rows"
            
            return preview
            
        except Exception as e:
            return f"CSV preview error: {e}"


if __name__ == "__main__":
    # Test the export manager
    print("🧪 Testing Export Manager")
    print("=" * 40)
    
    try:
        export_manager = ExportManager()
        
        # Create sample data
        sample_data = [
            {
                "equipment_name": "Test Pump",
                "part_name": "Impeller",
                "part_number": "P001",
                "quantity": "2",
                "pdf_reference": 1
            },
            {
                "equipment_name": "Test Motor",
                "part_name": "Bearing",
                "part_number": "B001",
                "quantity": "4",
                "pdf_reference": 2
            }
        ]
        
        # Test exports
        json_file = export_manager.export_to_json(sample_data, "test")
        csv_file = export_manager.export_to_csv(sample_data, "test")
        excel_file = export_manager.export_to_excel(sample_data, "test")
        
        print(f"📄 JSON: {json_file}")
        print(f"📊 CSV: {csv_file}")
        print(f"📈 Excel: {excel_file}")
        
        # Test preview
        preview = export_manager.create_csv_preview(sample_data)
        print(f"👀 CSV Preview:\n{preview}")
        
        # Test summary
        summary = export_manager.generate_export_summary(sample_data, "test.pdf", "langchain")
        print(f"📋 Summary:\n{summary}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
