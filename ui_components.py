#!/usr/bin/env python3
"""
UI components for structured data extraction system.
Handles Gradio interface creation and event handling.
"""

import gradio as gr
import json
from typing import List, Dict, <PERSON><PERSON>
from datetime import datetime

# Import our modules
from config_loader import load_available_extractions, get_extraction_names, create_extraction_folders
from search_utils import SearchManager
from data_extractor import DataExtractor
from export_utils import ExportManager


class StructuredQueryUI:
    """Main UI class for structured data extraction."""
    
    def __init__(self):
        """Initialize the UI with required components."""
        # Ensure folders exist
        create_extraction_folders()
        
        # Initialize components
        self.search_manager = SearchManager()
        self.data_extractor = DataExtractor()
        self.export_manager = ExportManager()
        
        # Load available extractions
        self.extractions = load_available_extractions()
        
        print(f"🚀 Initialized UI with {len(self.extractions)} extraction types")
    
    def get_available_files(self) -> List[str]:
        """Get available PDF files."""
        return self.search_manager.get_available_files()
    
    def process_extraction_request(self,
                                 extraction_type: str,
                                 file_name: str,
                                 use_semantic_search: bool,
                                 max_chunks: int = 20) -> Tuple[str, str, str, str]:
        """Process extraction request and return results with chunk display."""
        
        try:
            # Get extraction configuration
            extraction_config = self.extractions.get(extraction_type, {})
            if not extraction_config:
                return "❌ Invalid extraction type", "", "", ""
            
            system_prompt = extraction_config['system_prompt']
            search_queries = extraction_config.get('search_queries', [])
            
            # Get chunks based on search method
            if use_semantic_search and search_queries:
                # Generate smart search query
                smart_query = self.search_manager.generate_smart_search_query(
                    extraction_type, search_queries
                )
                print(f"🧠 Generated search query: '{smart_query}'")
                
                # Perform semantic search
                chunks = self.search_manager.semantic_search(
                    smart_query, file_name, max_chunks
                )
                method_used = f"Smart semantic search for: {smart_query}"
            else:
                # Get all pages in order
                chunks = self.search_manager.get_file_pages_ordered(file_name)[:max_chunks]
                method_used = f"Sequential page processing (first {max_chunks} pages)"
            
            if not chunks:
                return "❌ No relevant content found", "", "", ""
            
            # Save chunks to temp folder for debugging
            temp_file = self.search_manager.save_chunks_to_temp(chunks, file_name.replace('.pdf', ''))
            
            # Create chunk display markdown
            chunk_display = self._create_chunk_display(chunks)
            
            # Extract structured data using LangChain
            result = self.data_extractor.extract_with_langchain(
                chunks=chunks,
                extraction_type=extraction_type,
                system_prompt=system_prompt,
                preserve_page_order=True
            )
            
            if result.get("success"):
                # Format results
                total_chunks = result.get('total_chunks', len(chunks))
                extraction_method = result.get('extraction_method', 'langchain')
                
                status = f"""✅ Extraction completed successfully!
                
📊 **Processing Summary:**
- Method: {method_used}
- Chunks processed: {total_chunks}
- Extraction engine: {extraction_method}
- Temp file: {temp_file}

🎯 **Results:** {len(result['data']) if isinstance(result['data'], list) else 1} items extracted"""
                
                # Format JSON output
                json_output = json.dumps(result['data'], indent=2, ensure_ascii=False)
                
                # Generate CSV preview
                csv_preview = self.export_manager.create_csv_preview(result['data'])
                
                return status, json_output, csv_preview, chunk_display
            else:
                error_msg = result.get('error', 'Unknown extraction error')
                return f"❌ Extraction failed: {error_msg}", "", "", chunk_display
                
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"❌ Processing error: {error_details}")
            return f"❌ Processing error: {str(e)}", "", "", ""
    
    def _create_chunk_display(self, chunks: List[Dict]) -> str:
        """Create markdown display of retrieved chunks."""
        if not chunks:
            return "No chunks retrieved."
        
        markdown = f"# Retrieved Chunks ({len(chunks)} total)\n\n"
        markdown += f"**Retrieved at:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        for i, chunk in enumerate(chunks, 1):
            page_num = chunk.get('page_number', 'Unknown')
            file_name = chunk.get('file_name', 'Unknown')
            similarity = chunk.get('similarity', 0)
            content = chunk.get('text_raw', chunk.get('text', '')) or '' or ''
            
            markdown += f"## Chunk {i}\n\n"
            markdown += f"**📄 File:** {file_name}  \n"
            markdown += f"**📃 Page:** {page_num}  \n"
            if similarity > 0:
                markdown += f"**🎯 Similarity:** {similarity:.3f}  \n"
            markdown += f"**📏 Length:** {len(content)} characters\n\n"
            
            # Show first 500 characters of content
            preview_content = content[:500]
            if len(content) > 500:
                preview_content += "..."
            
            markdown += f"**📝 Content Preview:**\n```\n{preview_content}\n```\n\n"
            markdown += "---\n\n"
        
        return markdown
    
    def create_interface(self) -> gr.Blocks:
        """Create the Gradio interface."""
        
        with gr.Blocks(title="📊 Structured Data Extraction", theme=gr.themes.Soft()) as interface:
            gr.Markdown("# 📊 Structured Data Extraction System")
            gr.Markdown("Extract structured data from PDF documents using AI-powered analysis.")
            
            with gr.Tabs():
                # Main extraction tab
                with gr.TabItem("🔍 Extract Data"):
                    with gr.Row():
                        with gr.Column(scale=1):
                            extraction_dropdown = gr.Dropdown(
                                choices=get_extraction_names(),
                                label="📋 Extraction Type",
                                value=get_extraction_names()[0] if get_extraction_names() else None
                            )
                            
                            file_dropdown = gr.Dropdown(
                                choices=self.get_available_files(),
                                label="📄 Select PDF File",
                                value=self.get_available_files()[0] if self.get_available_files() else None
                            )
                            
                            semantic_search_checkbox = gr.Checkbox(
                                label="🧠 Use Smart Semantic Search",
                                value=True,
                                info="AI generates optimal search queries automatically"
                            )
                            
                            max_chunks_slider = gr.Slider(
                                minimum=5,
                                maximum=50,
                                value=20,
                                step=5,
                                label="📊 Max Chunks to Process"
                            )
                            
                            extract_button = gr.Button("🚀 Extract Data", variant="primary")
                        
                        with gr.Column(scale=2):
                            status_output = gr.Textbox(
                                label="📈 Processing Status",
                                lines=8,
                                max_lines=15
                            )
                    
                    with gr.Row():
                        with gr.Column():
                            json_output = gr.Code(
                                label="📄 JSON Output",
                                language="json",
                                lines=15
                            )
                        
                        with gr.Column():
                            csv_preview = gr.Textbox(
                                label="📊 CSV Preview",
                                lines=15,
                                max_lines=20
                            )
                
                # Chunk display tab
                with gr.TabItem("📝 Retrieved Chunks"):
                    gr.Markdown("## 📝 View Retrieved Chunks")
                    gr.Markdown("This tab shows the chunks retrieved from the PDF for extraction analysis.")
                    
                    chunk_display = gr.Markdown(
                        value="No chunks retrieved yet. Run an extraction to see chunks here.",
                        label="Retrieved Chunks"
                    )
                
                # Help tab
                with gr.TabItem("❓ Help"):
                    gr.Markdown("""
                    ## 🔧 How to Use
                    
                    1. **Select Extraction Type**: Choose the type of data you want to extract
                    2. **Choose PDF File**: Select from available PDF files in the database
                    3. **Configure Search**: Enable smart semantic search for better results
                    4. **Set Chunk Limit**: Control how many chunks to process (more = thorough, slower)
                    5. **Extract**: Click the extract button to process the document
                    
                    ## 📊 Features
                    
                    - **🧠 Smart Search**: AI generates optimal search queries automatically
                    - **🔧 LangChain Integration**: Robust structured data extraction
                    - **📝 Chunk Display**: View retrieved chunks for debugging
                    - **💾 Temp Storage**: Chunks saved to temp folder for analysis
                    - **📈 Multiple Formats**: JSON and CSV output available
                    
                    ## 📁 File Organization
                    
                    - `system_prompts/`: Extraction instructions
                    - `schemas/`: Data structure definitions  
                    - `search_queries/`: Predefined search terms
                    - `temp/`: Retrieved chunks for debugging
                    - `exports/`: Exported data files
                    """)
            
            # Event handlers
            extract_button.click(
                fn=self.process_extraction_request,
                inputs=[
                    extraction_dropdown,
                    file_dropdown,
                    semantic_search_checkbox,
                    max_chunks_slider
                ],
                outputs=[status_output, json_output, csv_preview, chunk_display]
            )
        
        return interface


def create_and_launch_interface():
    """Create and launch the Gradio interface."""
    print("🚀 Starting Structured Data Extraction Interface...")
    
    try:
        ui = StructuredQueryUI()
        interface = ui.create_interface()
        
        # Launch interface
        interface.launch(
            server_name="0.0.0.0",
            server_port=7861,
            share=False,
            show_error=True
        )
        
    except Exception as e:
        print(f"❌ Failed to start interface: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    create_and_launch_interface()
