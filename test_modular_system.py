#!/usr/bin/env python3
"""
Test script for the modular structured data extraction system.
Tests each module individually and then the integrated system.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_config_loader():
    """Test the configuration loader module."""
    print("🧪 Testing Config Loader")
    print("-" * 30)
    
    try:
        from config_loader import (
            load_available_extractions, 
            get_extraction_names, 
            validate_extraction_folders,
            create_extraction_folders
        )
        
        # Test folder creation
        create_extraction_folders()
        
        # Test folder validation
        folder_status = validate_extraction_folders()
        print(f"📁 Folder status: {folder_status}")
        
        # Test extraction loading
        extractions = load_available_extractions()
        print(f"📋 Available extractions: {list(extractions.keys())}")
        
        # Test extraction names
        names = get_extraction_names()
        print(f"🏷️ Extraction names: {names}")
        
        print("✅ Config Loader: PASSED\n")
        return True
        
    except Exception as e:
        print(f"❌ Config Loader: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_extractor():
    """Test the data extractor module."""
    print("🧪 Testing Data Extractor")
    print("-" * 30)
    
    try:
        from data_extractor import DataExtractor
        
        # Initialize extractor
        extractor = DataExtractor()
        print("✅ DataExtractor initialized")
        
        # Test with sample data
        sample_chunks = [
            {
                "page_number": 1,
                "file_name": "test.pdf",
                "text_raw": "SPARE PARTS LIST\nPump Assembly - Part No: P001\nQuantity: 2 units\nMaterial: Stainless Steel"
            }
        ]
        
        # Test LangChain extraction
        result = extractor.extract_with_langchain(
            chunks=sample_chunks,
            extraction_type="spare_parts",
            system_prompt="Extract spare parts information including part names, numbers, and quantities."
        )
        
        print(f"✅ Extraction result: {result.get('success', False)}")
        print(f"📊 Items extracted: {len(result.get('data', []))}")
        print(f"🔧 Method: {result.get('extraction_method', 'unknown')}")
        
        print("✅ Data Extractor: PASSED\n")
        return True
        
    except Exception as e:
        print(f"❌ Data Extractor: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False


def test_export_utils():
    """Test the export utilities module."""
    print("🧪 Testing Export Utils")
    print("-" * 30)
    
    try:
        from export_utils import ExportManager
        
        # Initialize export manager
        export_manager = ExportManager()
        print("✅ ExportManager initialized")
        
        # Test with sample data
        sample_data = [
            {
                "equipment_name": "Test Pump",
                "part_name": "Impeller",
                "part_number": "P001",
                "quantity": "2",
                "pdf_reference": 1
            }
        ]
        
        # Test CSV preview
        preview = export_manager.create_csv_preview(sample_data)
        print(f"✅ CSV preview generated: {len(preview)} characters")
        
        # Test summary generation
        summary = export_manager.generate_export_summary(sample_data, "test.pdf", "langchain")
        print(f"✅ Summary generated: {len(summary)} characters")
        
        print("✅ Export Utils: PASSED\n")
        return True
        
    except Exception as e:
        print(f"❌ Export Utils: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False


def test_search_utils():
    """Test the search utilities module (without Supabase connection)."""
    print("🧪 Testing Search Utils (Basic)")
    print("-" * 30)
    
    try:
        # Test if we can import the module
        from search_utils import SearchManager
        
        # Check if we have credentials
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_ANON_KEY")
        openai_key = os.getenv("OPENAI_API_KEY")
        
        print(f"🔑 Supabase URL: {'✅' if supabase_url else '❌'}")
        print(f"🔑 Supabase Key: {'✅' if supabase_key else '❌'}")
        print(f"🔑 OpenAI Key: {'✅' if openai_key else '❌'}")
        
        if supabase_url and supabase_key and openai_key:
            # Try to initialize if we have credentials
            search_manager = SearchManager()
            print("✅ SearchManager initialized")
            
            # Test query generation
            sample_queries = ["spare parts", "components", "parts list"]
            smart_query = search_manager.generate_smart_search_query("spare_parts", sample_queries)
            print(f"✅ Smart query generated: '{smart_query}'")
            
            print("✅ Search Utils: PASSED\n")
        else:
            print("⚠️ Search Utils: SKIPPED (missing credentials)\n")
        
        return True
        
    except Exception as e:
        print(f"❌ Search Utils: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backward_compatibility():
    """Test backward compatibility functions."""
    print("🧪 Testing Backward Compatibility")
    print("-" * 30)
    
    try:
        # Test importing the main functions
        from structured_query import (
            load_available_extractions,
            get_extraction_names,
            process_extraction_request
        )
        
        print("✅ Main functions imported successfully")
        
        # Test loading extractions
        extractions = load_available_extractions()
        print(f"📋 Loaded {len(extractions)} extraction types")
        
        # Test getting names
        names = get_extraction_names()
        print(f"🏷️ Got {len(names)} extraction names")
        
        print("✅ Backward Compatibility: PASSED\n")
        return True
        
    except Exception as e:
        print(f"❌ Backward Compatibility: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Testing Modular Structured Data Extraction System")
    print("=" * 60)
    
    tests = [
        test_config_loader,
        test_data_extractor,
        test_export_utils,
        test_search_utils,
        test_backward_compatibility
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("📊 Test Summary")
    print("=" * 60)
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The modular system is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
